import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../cubits/remote_config/remote_config_cubit.dart';
import '../cubits/template/template_cubit.dart';
import '../widgets/remote_config_builder.dart';
import '../widgets/templates/template_card.dart';
import '../../data/models/resume_template_model.dart';
import '../../core/utils/app_icons.dart';

class RemoteConfigDemoPage extends StatelessWidget {
  const RemoteConfigDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Remote Config Demo'),
        actions: [
          IconButton(
            icon: const Icon(AppIcons.refresh),
            onPressed: () {
              context.read<RemoteConfigCubit>().fetchConfig();
            },
            tooltip: 'Refresh Config',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Remote Config Status
            _buildSection(
              'Remote Config Status',
              RemoteConfigBuilder(
                builder: (context, config) {
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.check_circle, color: Colors.green),
                              SizedBox(width: 8),
                              Text('Remote Config Loaded'),
                            ],
                          ),
                          SizedBox(height: 8),
                          Text('Max Free Resumes: ${config.maxFreeResumes}'),
                          Text('Premium Templates: ${config.enablePremiumTemplates ? "Enabled" : "Disabled"}'),
                          Text('Banner Ads: ${config.showBannerAds ? "Enabled" : "Disabled"}'),
                          Text('Interstitial Ads: ${config.showInterstitialAds ? "Enabled" : "Disabled"}'),
                        ],
                      ),
                    ),
                  );
                },
                loading: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(width: 16),
                        Text('Loading Remote Config...'),
                      ],
                    ),
                  ),
                ),
                error: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(Icons.error, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Failed to load Remote Config'),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // Template Visibility Control
            _buildSection(
              'Template Visibility Control',
              BlocBuilder<TemplateCubit, TemplateState>(
                builder: (context, state) {
                  final allTemplates = TemplateRepository.getAllTemplates();
                  final visibleTemplates = state.templates;
                  
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Total Templates: ${allTemplates.length}',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          Text(
                            'Visible Templates: ${visibleTemplates.length}',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              context.read<TemplateCubit>().refreshTemplatesWithRemoteConfig();
                            },
                            child: Text('Refresh Templates'),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Feature Flags Demo
            _buildSection(
              'Feature Flags',
              Column(
                children: [
                  FeatureFlag(
                    featureKey: 'enable_premium_templates',
                    child: Card(
                      color: Colors.blue.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Icon(Icons.star, color: Colors.blue),
                            SizedBox(width: 8),
                            Text('Premium Templates Available!'),
                          ],
                        ),
                      ),
                    ),
                    fallback: Card(
                      color: Colors.grey.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Icon(Icons.star_border, color: Colors.grey),
                            SizedBox(width: 8),
                            Text('Premium Templates Disabled'),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 8),
                  FeatureFlag(
                    featureKey: 'enable_dark_mode',
                    child: Card(
                      color: Colors.purple.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Icon(Icons.dark_mode, color: Colors.purple),
                            SizedBox(width: 8),
                            Text('Dark Mode Available'),
                          ],
                        ),
                      ),
                    ),
                    fallback: Card(
                      color: Colors.grey.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Icon(Icons.light_mode, color: Colors.grey),
                            SizedBox(width: 8),
                            Text('Dark Mode Disabled'),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Ad Control Demo
            _buildSection(
              'Ad Control',
              Column(
                children: [
                  AdWidget(
                    adType: AdType.banner,
                    child: Container(
                      height: 60,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.yellow.shade100,
                        border: Border.all(color: Colors.yellow.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          'Banner Ad Placeholder',
                          style: TextStyle(color: Colors.yellow.shade800),
                        ),
                      ),
                    ),
                    fallback: Container(
                      height: 60,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          'Banner Ads Disabled',
                          style: TextStyle(color: Colors.grey.shade600),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      final cubit = context.read<RemoteConfigCubit>();
                      if (cubit.shouldShowInterstitialAds()) {
                        _showInterstitialAdDialog(context);
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Interstitial ads are disabled')),
                        );
                      }
                    },
                    child: Text('Show Interstitial Ad'),
                  ),
                ],
              ),
            ),

            // Template Grid with Remote Config
            _buildSection(
              'Filtered Templates',
              BlocBuilder<TemplateCubit, TemplateState>(
                builder: (context, state) {
                  if (state.templates.isEmpty) {
                    return Card(
                      child: Padding(
                        padding: const EdgeInsets.all(32),
                        child: Center(
                          child: Column(
                            children: [
                              Icon(Icons.visibility_off, size: 48, color: Colors.grey),
                              SizedBox(height: 16),
                              Text('No templates visible'),
                              Text('Check Remote Config settings'),
                            ],
                          ),
                        ),
                      ),
                    );
                  }

                  return GridView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.75,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                    ),
                    itemCount: state.templates.length,
                    itemBuilder: (context, index) {
                      final template = state.templates[index];
                      return TemplateCard(
                        template: template,
                        isSelected: false,
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Selected: ${template.name}')),
                          );
                        },
                        onPreview: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Preview: ${template.name}')),
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        child,
        SizedBox(height: 24),
      ],
    );
  }

  void _showInterstitialAdDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Interstitial Ad'),
        content: Text('This would show a full-screen interstitial ad.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}
