import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';

/// Centralized icon management for the Resume Builder app
/// Maps Material Icons to Lucide Icons for consistent design
class AppIcons {
  // Navigation Icons
  static const IconData dashboard = LucideIcons.layoutGrid;
  static const IconData folder = LucideIcons.folder;
  static const IconData editDocument = LucideIcons.edit;
  static const IconData templates = LucideIcons.fileText;
  static const IconData person = LucideIcons.user;
  static const IconData profile = LucideIcons.userCircle;

  // Resume Section Icons
  static const IconData summary = LucideIcons.user;
  static const IconData workExperience = LucideIcons.briefcase;
  static const IconData education = LucideIcons.graduationCap;
  static const IconData skills = LucideIcons.star;
  static const IconData projects = LucideIcons.code;
  static const IconData certifications = LucideIcons.award;
  static const IconData languages = LucideIcons.globe;
  static const IconData socialMedia = LucideIcons.share2;

  // Activity Icons
  static const IconData resumeCreated = LucideIcons.filePlus;
  static const IconData resumeUpdated = LucideIcons.edit;
  static const IconData resumeSaved = LucideIcons.save;
  static const IconData resumeExported = LucideIcons.download;
  static const IconData sectionEdited = LucideIcons.edit3;
  static const IconData templateUsed = LucideIcons.palette;
  static const IconData resumeViewed = LucideIcons.eye;
  static const IconData resumeDeleted = LucideIcons.trash2;

  // Action Icons
  static const IconData add = LucideIcons.plus;
  static const IconData edit = LucideIcons.edit;
  static const IconData delete = LucideIcons.trash2;
  static const IconData save = LucideIcons.save;
  static const IconData download = LucideIcons.download;
  static const IconData upload = LucideIcons.upload;
  static const IconData share = LucideIcons.share;
  static const IconData copy = LucideIcons.copy;
  static const IconData search = LucideIcons.search;
  static const IconData filter = LucideIcons.filter;
  static const IconData sort = LucideIcons.arrowUpDown;

  // Status Icons
  static const IconData checkCircle = LucideIcons.checkCircle;
  static const IconData warning = LucideIcons.alertTriangle;
  static const IconData error = LucideIcons.alertCircle;
  static const IconData info = LucideIcons.info;
  static const IconData success = LucideIcons.check;

  // Purchase/Premium Icons
  static const IconData star = LucideIcons.star;
  static const IconData crown = LucideIcons.crown;
  static const IconData premium = LucideIcons.gem;
  static const IconData calendar = LucideIcons.calendar;
  static const IconData calendarMonth = LucideIcons.calendar;
  static const IconData infinity = LucideIcons.infinity;
  static const IconData restore = LucideIcons.rotateCcw;
  static const IconData shoppingCart = LucideIcons.shoppingCart;

  // UI Elements
  static const IconData menu = LucideIcons.menu;
  static const IconData close = LucideIcons.x;
  static const IconData back = LucideIcons.arrowLeft;
  static const IconData forward = LucideIcons.arrowRight;
  static const IconData up = LucideIcons.arrowUp;
  static const IconData down = LucideIcons.arrowDown;
  static const IconData expand = LucideIcons.expand;
  static const IconData collapse = LucideIcons.minimize;
  static const IconData refresh = LucideIcons.refreshCw;
  static const IconData settings = LucideIcons.settings;
  static const IconData help = LucideIcons.helpCircle;

  // Form Icons
  static const IconData email = LucideIcons.mail;
  static const IconData phone = LucideIcons.phone;
  static const IconData location = LucideIcons.mapPin;
  static const IconData website = LucideIcons.globe;
  static const IconData linkedin = LucideIcons.linkedin;
  static const IconData github = LucideIcons.github;
  static const IconData twitter = LucideIcons.twitter;

  // File Icons
  static const IconData file = LucideIcons.file;
  static const IconData filePdf = LucideIcons.fileText;
  static const IconData fileImage = LucideIcons.image;
  static const IconData fileDoc = LucideIcons.fileText;

  // Theme Icons
  static const IconData lightMode = LucideIcons.sun;
  static const IconData darkMode = LucideIcons.moon;
  static const IconData autoMode = LucideIcons.monitor;

  // Utility method to get section icon by name
  static IconData getSectionIcon(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'summary':
      case 'about':
      case 'profile':
        return summary;
      case 'work experience':
      case 'experience':
      case 'employment':
        return workExperience;
      case 'education':
      case 'academic':
        return education;
      case 'skills':
      case 'technical skills':
      case 'soft skills':
        return skills;
      case 'projects':
      case 'portfolio':
        return projects;
      case 'certifications':
      case 'certificates':
      case 'awards':
        return certifications;
      case 'languages':
      case 'language skills':
        return languages;
      case 'social media':
      case 'social':
      case 'links':
        return socialMedia;
      default:
        return LucideIcons.circle;
    }
  }

  // Utility method to get activity icon by type
  static IconData getActivityIcon(String activityType) {
    switch (activityType.toLowerCase()) {
      case 'resume_created':
      case 'created':
        return resumeCreated;
      case 'resume_updated':
      case 'updated':
        return resumeUpdated;
      case 'resume_saved':
      case 'saved':
        return resumeSaved;
      case 'resume_exported':
      case 'exported':
        return resumeExported;
      case 'section_edited':
      case 'edited':
        return sectionEdited;
      case 'template_used':
      case 'template':
        return templateUsed;
      case 'resume_viewed':
      case 'viewed':
        return resumeViewed;
      case 'resume_deleted':
      case 'deleted':
        return resumeDeleted;
      default:
        return info;
    }
  }

  // Utility method to get plan icon by product ID
  static IconData getPlanIcon(String productId) {
    switch (productId.toLowerCase()) {
      case 'premium_monthly':
      case 'monthly':
        return calendar;
      case 'premium_yearly':
      case 'yearly':
        return calendar;
      case 'premium_lifetime':
      case 'lifetime':
        return infinity;
      default:
        return star;
    }
  }
}
